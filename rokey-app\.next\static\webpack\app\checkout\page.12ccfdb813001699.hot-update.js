"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkout/page",{

/***/ "(app-pages-browser)/./src/app/checkout/page.tsx":
/*!***********************************!*\
  !*** ./src/app/checkout/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckoutPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction CheckoutPageContent() {\n    _s();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Only run on client side\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckoutPageContent.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"CheckoutPageContent.useEffect\"], []);\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading checkout...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActualCheckoutContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 28,\n        columnNumber: 10\n    }, this);\n}\n_s(CheckoutPageContent, \"k460N28PNzD7zo1YW47Q9UigQis=\");\n_c = CheckoutPageContent;\nfunction ActualCheckoutContent() {\n    _s1();\n    // Immediate debug log\n    console.log('🔥 ActualCheckoutContent function called');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__.createClientComponentClient)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const selectedPlan = searchParams.get('plan') || 'professional';\n    const userId = searchParams.get('user_id');\n    const email = searchParams.get('email');\n    const isSignup = searchParams.get('signup') === 'true';\n    // Debug the URL params immediately\n    console.log('🔍 URL params parsed:', {\n        selectedPlan,\n        userId,\n        email,\n        isSignup\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 CheckoutPageContent component mounting...');\n            fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'COMPONENT_MOUNT',\n                    message: 'CheckoutPageContent component is mounting'\n                })\n            }).catch({\n                \"ActualCheckoutContent.useEffect\": ()=>{}\n            }[\"ActualCheckoutContent.useEffect\"]);\n            setMounted(true);\n        }\n    }[\"ActualCheckoutContent.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            if (!mounted) return;\n            console.log('=== CHECKOUT PAGE MOUNTED ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            console.log('Current URL:', window.location.href);\n            // Check localStorage for pending signup\n            const pendingSignup = localStorage.getItem('pending_signup');\n            console.log('localStorage pending_signup:', pendingSignup ? 'FOUND' : 'NOT FOUND');\n            if (pendingSignup) {\n                console.log('Pending signup data:', JSON.parse(pendingSignup));\n            }\n            // Add debug function to window for manual testing\n            window.debugCheckout = ({\n                \"ActualCheckoutContent.useEffect\": ()=>{\n                    console.log('=== DEBUG CHECKOUT ===');\n                    console.log('localStorage pending_signup:', localStorage.getItem('pending_signup'));\n                    console.log('URL params:', {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    });\n                }\n            })[\"ActualCheckoutContent.useEffect\"];\n            initializeCheckout();\n        }\n    }[\"ActualCheckoutContent.useEffect\"], [\n        mounted\n    ]);\n    const initializeCheckout = async ()=>{\n        try {\n            // Send debug info to server for terminal logging\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CHECKOUT_INITIALIZATION',\n                    urlParams: {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    },\n                    currentUrl: window.location.href,\n                    localStorage: {\n                        pending_signup: localStorage.getItem('pending_signup') ? 'FOUND' : 'NOT_FOUND',\n                        pending_signup_data: localStorage.getItem('pending_signup')\n                    }\n                })\n            }).catch(()=>{}); // Don't fail if debug endpoint fails\n            console.log('=== CHECKOUT INITIALIZATION ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            // ALWAYS prioritize signup flow if URL indicates signup\n            console.log('🔍 Checking isSignup:', isSignup, typeof isSignup);\n            if (isSignup) {\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'SIGNUP_FLOW_DETECTED',\n                        urlParams: {\n                            selectedPlan,\n                            userId,\n                            email,\n                            isSignup\n                        },\n                        isSignupType: typeof isSignup,\n                        isSignupValue: isSignup\n                    })\n                }).catch(()=>{});\n                console.log('=== SIGNUP FLOW DETECTED ===');\n                // Check for pending signup data\n                const pendingSignup = localStorage.getItem('pending_signup');\n                console.log('Pending signup data:', pendingSignup ? 'FOUND' : 'NOT FOUND');\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'PENDING_SIGNUP_CHECK',\n                        found: !!pendingSignup,\n                        data: pendingSignup\n                    })\n                }).catch(()=>{});\n                if (pendingSignup) {\n                    const userData = JSON.parse(pendingSignup);\n                    console.log('Processing signup with data:', userData);\n                    await createCheckoutSessionForSignup(userData);\n                    return;\n                } else {\n                    console.log('ERROR: Signup URL but no localStorage data');\n                    await fetch('/api/debug/checkout', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            action: 'ERROR_NO_LOCALSTORAGE',\n                            message: 'Signup URL detected but no localStorage data found'\n                        })\n                    }).catch(()=>{});\n                    setError('Signup session expired. Please try signing up again.');\n                    setTimeout(()=>router.push(\"/auth/signup?plan=\".concat(selectedPlan)), 3000);\n                    return;\n                }\n            }\n            // EXISTING USER FLOW - Only if not a signup\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'EXISTING_USER_FLOW',\n                    message: 'No signup parameter detected, checking for existing user session'\n                })\n            }).catch(()=>{});\n            console.log('=== EXISTING USER FLOW ===');\n            const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n            console.log('Session check:', {\n                hasSession: !!session,\n                error: sessionError\n            });\n            if (sessionError || !session) {\n                console.log('No valid session - redirecting to signin');\n                setError('Authentication required. Please sign in first.');\n                setTimeout(()=>router.push(\"/auth/signin?plan=\".concat(selectedPlan)), 3000);\n                return;\n            }\n            setUser(session.user);\n            console.log('Processing existing user checkout:', session.user.id);\n            await createCheckoutSession(session.user.id);\n        } catch (error) {\n            console.error('Checkout initialization error:', error);\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'ERROR',\n                    error: error.message\n                })\n            }).catch(()=>{});\n            setError('Failed to initialize checkout. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const createCheckoutSessionForSignup = async (userData)=>{\n        try {\n            const requestData = {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userEmail: userData.email,\n                signup: true,\n                pendingUserData: userData\n            };\n            console.log('Creating checkout session for signup:', requestData);\n            console.log('Environment check:', {\n                hasStarterPrice: !!\"price_1RaA5xC97XFBBUvdt12n1i0T\",\n                hasProfessionalPrice: !!\"price_1RaABVC97XFBBUvdkZZc1oQB\",\n                hasEnterprisePrice: !!\"price_1RaADDC97XFBBUvd7j6OPJj7\"\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            const data = await response.json();\n            console.log('Checkout session response:', {\n                ok: response.ok,\n                status: response.status,\n                data\n            });\n            if (!response.ok) {\n                console.error('Checkout session creation failed:', data);\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Signup checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const createCheckoutSession = async (userId)=>{\n        try {\n            // Get user email from session or localStorage\n            let userEmail = user === null || user === void 0 ? void 0 : user.email;\n            if (!userEmail) {\n                userEmail = localStorage.getItem('pending_email');\n            }\n            if (!userEmail) {\n                throw new Error('User email not found');\n            }\n            console.log('Creating checkout session with:', {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userId: userId,\n                userEmail: userEmail\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    priceId: getPriceId(selectedPlan),\n                    tier: selectedPlan,\n                    userId: userId,\n                    userEmail: userEmail\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const getPriceId = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return \"price_1RaA5xC97XFBBUvdt12n1i0T\" || 0;\n            case 'professional':\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n            case 'enterprise':\n                return \"price_1RaADDC97XFBBUvd7j6OPJj7\" || 0;\n            default:\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n        }\n    };\n    const getPlanPrice = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return '$29';\n            case 'professional':\n                return '$99';\n            case 'enterprise':\n                return '$299';\n            default:\n                return '$99';\n        }\n    };\n    // Show loading until component is mounted (prevents hydration issues)\n    if (!mounted) {\n        console.log('⏳ Component not mounted yet, showing loading...');\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading checkout...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 329,\n            columnNumber: 7\n        }, this);\n    }\n    console.log('✅ Component mounted, proceeding with render...');\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Checkout Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                        href: \"/auth/signup?plan=\".concat(selectedPlan),\n                        className: \"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#e55a2b] transition-colors\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 343,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 342,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/roukey_logo.png\",\n                            alt: \"RouKey\",\n                            width: 40,\n                            height: 40,\n                            className: \"w-10 h-10 object-contain\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Setting up your subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#ff6b35] font-semibold\",\n                                            children: [\n                                                selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1),\n                                                \" Plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        getPlanPrice(selectedPlan),\n                                        \"/month\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"You'll be redirected to Stripe to complete your payment securely.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"After payment, you'll verify your email and gain access to your dashboard.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex items-center justify-center space-x-2 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Secured by Stripe\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 364,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 363,\n        columnNumber: 5\n    }, this);\n}\n_s1(ActualCheckoutContent, \"28X6dCLSY33yGsWYE1GDYj1wduQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c1 = ActualCheckoutContent;\nfunction CheckoutPage() {\n    console.log('🚀 CheckoutPage main function called');\n    // Force client-side rendering by checking if window exists\n    if (false) {}\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckoutPageContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 432,\n        columnNumber: 10\n    }, this);\n}\n_c2 = CheckoutPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CheckoutPageContent\");\n$RefreshReg$(_c1, \"ActualCheckoutContent\");\n$RefreshReg$(_c2, \"CheckoutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/checkout/page.tsx\n"));

/***/ })

});