'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import Image from 'next/image';
import Link from 'next/link';
import dynamic from 'next/dynamic';

function CheckoutPageContent() {
  console.log('🔥 CheckoutPageContent function called');
  return <ActualCheckoutContent />;
}

function ActualCheckoutContent() {
  // Immediate debug log
  console.log('🔥 ActualCheckoutContent function called');

  // React hooks must be at the top level
  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createClientComponentClient();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<any>(null);
  const [mounted, setMounted] = useState(false);

  console.log('🔥 ActualCheckoutContent - all hooks initialized');

  const selectedPlan = searchParams.get('plan') || 'professional';
  const userId = searchParams.get('user_id');
  const email = searchParams.get('email');
  const isSignup = searchParams.get('signup') === 'true';

  // Debug the URL params immediately
  console.log('🔍 ActualCheckoutContent URL params parsed:', { selectedPlan, userId, email, isSignup });

  useEffect(() => {
    console.log('🚀 ActualCheckoutContent first useEffect - component mounting...');
    fetch('/api/debug/checkout', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'COMPONENT_MOUNT',
        message: 'ActualCheckoutContent component is mounting'
      })
    }).catch(() => {});
    console.log('🚀 ActualCheckoutContent - setting mounted to true');
    setMounted(true);
  }, []);

  useEffect(() => {
    console.log('🚀 ActualCheckoutContent second useEffect - mounted:', mounted);
    if (!mounted) {
      console.log('🚀 ActualCheckoutContent - not mounted yet, returning');
      return;
    }

    console.log('=== CHECKOUT PAGE MOUNTED ===');
    console.log('URL params:', { selectedPlan, userId, email, isSignup });
    console.log('Current URL:', window.location.href);

    // Check localStorage for pending signup
    const pendingSignup = localStorage.getItem('pending_signup');
    console.log('localStorage pending_signup:', pendingSignup ? 'FOUND' : 'NOT FOUND');
    if (pendingSignup) {
      console.log('Pending signup data:', JSON.parse(pendingSignup));
    }

    // Add debug function to window for manual testing
    (window as any).debugCheckout = () => {
      console.log('=== DEBUG CHECKOUT ===');
      console.log('localStorage pending_signup:', localStorage.getItem('pending_signup'));
      console.log('URL params:', { selectedPlan, userId, email, isSignup });
    };

    initializeCheckout();
  }, [mounted]);

  const initializeCheckout = async () => {
    try {
      // Send debug info to server for terminal logging
      await fetch('/api/debug/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'CHECKOUT_INITIALIZATION',
          urlParams: { selectedPlan, userId, email, isSignup },
          currentUrl: window.location.href
        })
      }).catch(() => {}); // Don't fail if debug endpoint fails

      console.log('=== CHECKOUT INITIALIZATION ===');
      console.log('URL params:', { selectedPlan, userId, email, isSignup });

      // Get current user session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      console.log('Session check:', { hasSession: !!session, error: sessionError });

      // Debug session details
      await fetch('/api/debug/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'SESSION_CHECK',
          hasSession: !!session,
          sessionError: sessionError?.message,
          userId: session?.user?.id,
          userEmail: session?.user?.email
        })
      }).catch(() => {});

      if (sessionError || !session) {
        console.log('No valid session - redirecting to signup');
        console.log('Session error:', sessionError);
        console.log('Session data:', session);

        await fetch('/api/debug/checkout', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'SESSION_FAILED',
            error: sessionError?.message || 'No session found',
            redirecting: true
          })
        }).catch(() => {});

        setError('Authentication required. Please sign up first.');
        setTimeout(() => router.push(`/auth/signup?plan=${selectedPlan}`), 3000);
        return;
      }

      setUser(session.user);

      // Check if user has payment_pending status (new signup)
      const userMetadata = session.user.user_metadata;
      const paymentStatus = userMetadata?.payment_status;

      await fetch('/api/debug/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'USER_SESSION_FOUND',
          userId: session.user.id,
          email: session.user.email,
          paymentStatus,
          userMetadata
        })
      }).catch(() => {});

      console.log('Processing checkout for user:', session.user.id);
      console.log('Payment status:', paymentStatus);

      // Debug before calling createCheckoutSession
      await fetch('/api/debug/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'CALLING_CREATE_CHECKOUT_SESSION',
          userId: session.user.id,
          selectedPlan,
          aboutToCall: true
        })
      }).catch(() => {});

      console.log('About to call createCheckoutSession...');

      try {
        // Create checkout session for authenticated user
        await createCheckoutSession(session.user.id);
        console.log('createCheckoutSession call completed successfully');
      } catch (checkoutError) {
        console.error('Error in createCheckoutSession:', checkoutError);

        await fetch('/api/debug/checkout', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'CREATE_CHECKOUT_SESSION_ERROR',
            error: checkoutError.message,
            stack: checkoutError.stack
          })
        }).catch(() => {});

        throw checkoutError; // Re-throw to be caught by outer catch
      }

    } catch (error) {
      console.error('Checkout initialization error:', error);
      await fetch('/api/debug/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'ERROR',
          error: error.message
        })
      }).catch(() => {});
      setError('Failed to initialize checkout. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const createCheckoutSessionForSignup = async (userData: any) => {
    try {
      const requestData = {
        priceId: getPriceId(selectedPlan),
        tier: selectedPlan,
        userEmail: userData.email,
        signup: true, // Flag to indicate this is a signup
        pendingUserData: userData
      };

      console.log('Creating checkout session for signup:', requestData);
      console.log('Environment check:', {
        hasStarterPrice: !!process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID,
        hasProfessionalPrice: !!process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_PRICE_ID,
        hasEnterprisePrice: !!process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID
      });

      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const data = await response.json();

      console.log('Checkout session response:', { ok: response.ok, status: response.status, data });

      if (!response.ok) {
        console.error('Checkout session creation failed:', data);
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Redirect to Stripe Checkout
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error('No checkout URL received');
      }

    } catch (error) {
      console.error('Signup checkout session error:', error);
      setError(error instanceof Error ? error.message : 'Failed to start checkout');
    }
  };

  const createCheckoutSession = async (userId: string) => {
    console.log('🚀 createCheckoutSession function called with userId:', userId);

    try {
      // Get email from multiple sources
      const userEmail = user?.email || user?.user_metadata?.email || email;

      console.log('Email extraction debug:', {
        userEmail: user?.email,
        userMetadataEmail: user?.user_metadata?.email,
        urlEmail: email,
        finalEmail: userEmail
      });

      if (!userEmail) {
        console.error('No email found in any source:', { user, email });
        throw new Error('User email not found');
      }

      console.log('Creating checkout session with:', {
        priceId: getPriceId(selectedPlan),
        tier: selectedPlan,
        userId: userId,
        userEmail: userEmail
      });

      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId: getPriceId(selectedPlan),
          tier: selectedPlan,
          userId: userId,
          userEmail: userEmail,
          signup: false, // This is for existing authenticated users
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Redirect to Stripe Checkout
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error('No checkout URL received');
      }

    } catch (error) {
      console.error('Checkout session error:', error);
      setError(error instanceof Error ? error.message : 'Failed to start checkout');
    }
  };

  const getPriceId = (plan: string): string => {
    switch (plan.toLowerCase()) {
      case 'starter':
        return process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID || 'price_starter';
      case 'professional':
        return process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_PRICE_ID || 'price_professional';
      case 'enterprise':
        return process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise';
      default:
        return process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_PRICE_ID || 'price_professional';
    }
  };

  const getPlanPrice = (plan: string): string => {
    switch (plan.toLowerCase()) {
      case 'starter':
        return '$29';
      case 'professional':
        return '$99';
      case 'enterprise':
        return '$299';
      default:
        return '$99';
    }
  };

  // Show loading until component is mounted (prevents hydration issues)
  if (!mounted) {
    console.log('⏳ Component not mounted yet, showing loading...');
    return (
      <div className="fixed inset-0 bg-white z-[9999] flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading checkout...</p>
        </div>
      </div>
    );
  }

  console.log('✅ Component mounted, proceeding with render...');

  if (error) {
    return (
      <div className="fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4">
        <div className="max-w-md w-full text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Checkout Error</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link
            href={`/auth/signup?plan=${selectedPlan}`}
            className="inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#e55a2b] transition-colors"
          >
            Try Again
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4">
        <div className="max-w-md w-full text-center">
          {/* Logo */}
          <div className="flex justify-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center">
              <Image
                src="/roukey_logo.png"
                alt="RouKey"
                width={40}
                height={40}
                className="w-10 h-10 object-contain"
                priority
              />
            </div>
          </div>

          {/* Loading State */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
            <div className="w-12 h-12 border-4 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>

            <h1 className="text-2xl font-bold text-gray-900 mb-4">Setting up your subscription...</h1>

            <div className="bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl p-4 mb-6">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <div className="w-2 h-2 bg-[#ff6b35] rounded-full"></div>
                <span className="text-[#ff6b35] font-semibold">
                  {selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1)} Plan
                </span>
                <div className="w-2 h-2 bg-[#ff6b35] rounded-full"></div>
              </div>
              <p className="text-2xl font-bold text-gray-900">{getPlanPrice(selectedPlan)}/month</p>
            </div>

            <p className="text-gray-600 mb-4">
              You'll be redirected to Stripe to complete your payment securely.
            </p>

            <p className="text-sm text-gray-500">
              After payment, you'll verify your email and gain access to your dashboard.
            </p>
          </div>

          {/* Security Notice */}
          <div className="mt-6 flex items-center justify-center space-x-2 text-sm text-gray-500">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            <span>Secured by Stripe</span>
          </div>
        </div>
    </div>
  );
}

export default function CheckoutPage() {
  console.log('🚀 CheckoutPage main function called');
  return <CheckoutPageContent />;
}
