"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkout/page",{

/***/ "(app-pages-browser)/./src/app/checkout/page.tsx":
/*!***********************************!*\
  !*** ./src/app/checkout/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckoutPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CheckoutPageContent() {\n    console.log('🔥 CheckoutPageContent function called');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActualCheckoutContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n_c = CheckoutPageContent;\nfunction ActualCheckoutContent() {\n    _s();\n    // Immediate debug log\n    console.log('🔥 ActualCheckoutContent function called');\n    // React hooks must be at the top level\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__.createClientComponentClient)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    console.log('🔥 ActualCheckoutContent - all hooks initialized');\n    const selectedPlan = searchParams.get('plan') || 'professional';\n    const userId = searchParams.get('user_id');\n    const email = searchParams.get('email');\n    const isSignup = searchParams.get('signup') === 'true';\n    // Debug the URL params immediately\n    console.log('🔍 ActualCheckoutContent URL params parsed:', {\n        selectedPlan,\n        userId,\n        email,\n        isSignup\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 ActualCheckoutContent first useEffect - component mounting...');\n            fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'COMPONENT_MOUNT',\n                    message: 'ActualCheckoutContent component is mounting'\n                })\n            }).catch({\n                \"ActualCheckoutContent.useEffect\": ()=>{}\n            }[\"ActualCheckoutContent.useEffect\"]);\n            console.log('🚀 ActualCheckoutContent - setting mounted to true');\n            setMounted(true);\n        }\n    }[\"ActualCheckoutContent.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 ActualCheckoutContent second useEffect - mounted:', mounted);\n            if (!mounted) {\n                console.log('🚀 ActualCheckoutContent - not mounted yet, returning');\n                return;\n            }\n            console.log('=== CHECKOUT PAGE MOUNTED ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            console.log('Current URL:', window.location.href);\n            // Check localStorage for pending signup\n            const pendingSignup = localStorage.getItem('pending_signup');\n            console.log('localStorage pending_signup:', pendingSignup ? 'FOUND' : 'NOT FOUND');\n            if (pendingSignup) {\n                console.log('Pending signup data:', JSON.parse(pendingSignup));\n            }\n            // Add debug function to window for manual testing\n            window.debugCheckout = ({\n                \"ActualCheckoutContent.useEffect\": ()=>{\n                    console.log('=== DEBUG CHECKOUT ===');\n                    console.log('localStorage pending_signup:', localStorage.getItem('pending_signup'));\n                    console.log('URL params:', {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    });\n                }\n            })[\"ActualCheckoutContent.useEffect\"];\n            initializeCheckout();\n        }\n    }[\"ActualCheckoutContent.useEffect\"], [\n        mounted\n    ]);\n    const initializeCheckout = async ()=>{\n        try {\n            var _session_user, _session_user1;\n            // Send debug info to server for terminal logging\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CHECKOUT_INITIALIZATION',\n                    urlParams: {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    },\n                    currentUrl: window.location.href\n                })\n            }).catch(()=>{}); // Don't fail if debug endpoint fails\n            console.log('=== CHECKOUT INITIALIZATION ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            // Get current user session\n            const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n            console.log('Session check:', {\n                hasSession: !!session,\n                error: sessionError\n            });\n            // Debug session details\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'SESSION_CHECK',\n                    hasSession: !!session,\n                    sessionError: sessionError === null || sessionError === void 0 ? void 0 : sessionError.message,\n                    userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,\n                    userEmail: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email\n                })\n            }).catch(()=>{});\n            if (sessionError || !session) {\n                console.log('No valid session - redirecting to signup');\n                console.log('Session error:', sessionError);\n                console.log('Session data:', session);\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'SESSION_FAILED',\n                        error: (sessionError === null || sessionError === void 0 ? void 0 : sessionError.message) || 'No session found',\n                        redirecting: true\n                    })\n                }).catch(()=>{});\n                setError('Authentication required. Please sign up first.');\n                setTimeout(()=>router.push(\"/auth/signup?plan=\".concat(selectedPlan)), 3000);\n                return;\n            }\n            setUser(session.user);\n            console.log('Set user state:', session.user);\n            // Check if user has payment_pending status (new signup)\n            const userMetadata = session.user.user_metadata;\n            const paymentStatus = userMetadata === null || userMetadata === void 0 ? void 0 : userMetadata.payment_status;\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'USER_SESSION_FOUND',\n                    userId: session.user.id,\n                    email: session.user.email,\n                    paymentStatus,\n                    userMetadata\n                })\n            }).catch(()=>{});\n            console.log('Processing checkout for user:', session.user.id);\n            console.log('Payment status:', paymentStatus);\n            // Debug before calling createCheckoutSession\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CALLING_CREATE_CHECKOUT_SESSION',\n                    userId: session.user.id,\n                    selectedPlan,\n                    aboutToCall: true\n                })\n            }).catch(()=>{});\n            console.log('About to call createCheckoutSession...');\n            try {\n                // Create checkout session for authenticated user\n                // Pass the session user directly instead of relying on state\n                console.log('Calling createCheckoutSession with session user:', session.user);\n                await createCheckoutSession(session.user.id, session.user);\n                console.log('createCheckoutSession call completed successfully');\n            } catch (checkoutError) {\n                console.error('Error in createCheckoutSession:', checkoutError);\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'CREATE_CHECKOUT_SESSION_ERROR',\n                        error: checkoutError.message,\n                        stack: checkoutError.stack\n                    })\n                }).catch(()=>{});\n                throw checkoutError; // Re-throw to be caught by outer catch\n            }\n        } catch (error) {\n            console.error('Checkout initialization error:', error);\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'ERROR',\n                    error: error.message\n                })\n            }).catch(()=>{});\n            setError('Failed to initialize checkout. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const createCheckoutSessionForSignup = async (userData)=>{\n        try {\n            const requestData = {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userEmail: userData.email,\n                signup: true,\n                pendingUserData: userData\n            };\n            console.log('Creating checkout session for signup:', requestData);\n            console.log('Environment check:', {\n                hasStarterPrice: !!\"price_1RaA5xC97XFBBUvdt12n1i0T\",\n                hasProfessionalPrice: !!\"price_1RaABVC97XFBBUvdkZZc1oQB\",\n                hasEnterprisePrice: !!\"price_1RaADDC97XFBBUvd7j6OPJj7\"\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            const data = await response.json();\n            console.log('Checkout session response:', {\n                ok: response.ok,\n                status: response.status,\n                data\n            });\n            if (!response.ok) {\n                console.error('Checkout session creation failed:', data);\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Signup checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const createCheckoutSession = async (userId, sessionUser)=>{\n        console.log('🚀 createCheckoutSession function called with userId:', userId);\n        try {\n            var _currentUser_user_metadata, _currentUser_user_metadata1;\n            // Use passed sessionUser or fallback to state user\n            const currentUser = sessionUser || user;\n            // Get email from multiple sources\n            const userEmail = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_user_metadata = currentUser.user_metadata) === null || _currentUser_user_metadata === void 0 ? void 0 : _currentUser_user_metadata.email) || email;\n            console.log('Email extraction debug:', {\n                currentUserEmail: currentUser === null || currentUser === void 0 ? void 0 : currentUser.email,\n                currentUserMetadataEmail: currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_user_metadata1 = currentUser.user_metadata) === null || _currentUser_user_metadata1 === void 0 ? void 0 : _currentUser_user_metadata1.email,\n                urlEmail: email,\n                finalEmail: userEmail,\n                currentUserObject: currentUser\n            });\n            if (!userEmail) {\n                console.error('No email found in any source:', {\n                    currentUser,\n                    stateUser: user,\n                    urlEmail: email\n                });\n                throw new Error('User email not found');\n            }\n            console.log('Creating checkout session with:', {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userId: userId,\n                userEmail: userEmail\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    priceId: getPriceId(selectedPlan),\n                    tier: selectedPlan,\n                    userId: userId,\n                    userEmail: userEmail,\n                    signup: false\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const getPriceId = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return \"price_1RaA5xC97XFBBUvdt12n1i0T\" || 0;\n            case 'professional':\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n            case 'enterprise':\n                return \"price_1RaADDC97XFBBUvd7j6OPJj7\" || 0;\n            default:\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n        }\n    };\n    const getPlanPrice = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return '$29';\n            case 'professional':\n                return '$99';\n            case 'enterprise':\n                return '$299';\n            default:\n                return '$99';\n        }\n    };\n    // Show loading until component is mounted (prevents hydration issues)\n    if (!mounted) {\n        console.log('⏳ Component not mounted yet, showing loading...');\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading checkout...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 352,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 351,\n            columnNumber: 7\n        }, this);\n    }\n    console.log('✅ Component mounted, proceeding with render...');\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Checkout Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                        href: \"/auth/signup?plan=\".concat(selectedPlan),\n                        className: \"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#e55a2b] transition-colors\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 365,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 364,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/roukey_logo.png\",\n                            alt: \"RouKey\",\n                            width: 40,\n                            height: 40,\n                            className: \"w-10 h-10 object-contain\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Setting up your subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#ff6b35] font-semibold\",\n                                            children: [\n                                                selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1),\n                                                \" Plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        getPlanPrice(selectedPlan),\n                                        \"/month\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"You'll be redirected to Stripe to complete your payment securely.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"After payment, you'll verify your email and gain access to your dashboard.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 402,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex items-center justify-center space-x-2 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Secured by Stripe\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 428,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 386,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 385,\n        columnNumber: 5\n    }, this);\n}\n_s(ActualCheckoutContent, \"28X6dCLSY33yGsWYE1GDYj1wduQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c1 = ActualCheckoutContent;\nfunction CheckoutPage() {\n    console.log('🚀 CheckoutPage main function called');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckoutPageContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 441,\n        columnNumber: 10\n    }, this);\n}\n_c2 = CheckoutPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CheckoutPageContent\");\n$RefreshReg$(_c1, \"ActualCheckoutContent\");\n$RefreshReg$(_c2, \"CheckoutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/checkout/page.tsx\n"));

/***/ })

});