"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signup/page",{

/***/ "(app-pages-browser)/./src/app/auth/signup/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signup/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignUpPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SignUpPageContent() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: '',\n        lastName: '',\n        email: '',\n        password: '',\n        confirmPassword: ''\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [agreedToTerms, setAgreedToTerms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_5__.createClientComponentClient)();\n    // Note: Removed automatic session clearing to avoid conflicts\n    // Get selected plan from URL params\n    const selectedPlan = searchParams.get('plan');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SignUpPageContent.useEffect\": ()=>{\n            // Redirect to pricing if no plan is selected\n            if (!selectedPlan) {\n                router.push('/pricing');\n                return;\n            }\n            // Validate plan\n            if (![\n                'starter',\n                'professional',\n                'enterprise'\n            ].includes(selectedPlan)) {\n                router.push('/pricing');\n                return;\n            }\n            // Check if user is already signed in\n            const checkUser = {\n                \"SignUpPageContent.useEffect.checkUser\": async ()=>{\n                    const { data: { session } } = await supabase.auth.getSession();\n                    if (session) {\n                        console.log('User already signed in, checking subscription status...');\n                        // Check if user has active subscription\n                        const { data: profile } = await supabase.from('user_profiles').select('subscription_status').eq('user_id', session.user.id).single();\n                        if (profile && profile.subscription_status === 'active') {\n                            // User has active subscription, go to dashboard\n                            router.push('/dashboard');\n                        } else {\n                            // User needs to complete payment\n                            router.push(\"/checkout?plan=\".concat(selectedPlan, \"&user_id=\").concat(session.user.id));\n                        }\n                    }\n                }\n            }[\"SignUpPageContent.useEffect.checkUser\"];\n            checkUser();\n        }\n    }[\"SignUpPageContent.useEffect\"], [\n        router,\n        selectedPlan,\n        supabase\n    ]);\n    const handleChange = (e)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [e.target.name]: e.target.value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError('');\n        // Validation\n        if (formData.password !== formData.confirmPassword) {\n            setError('Passwords do not match');\n            setIsLoading(false);\n            return;\n        }\n        if (formData.password.length < 8) {\n            setError('Password must be at least 8 characters long');\n            setIsLoading(false);\n            return;\n        }\n        if (!agreedToTerms) {\n            setError('Please agree to the Terms of Service and Privacy Policy');\n            setIsLoading(false);\n            return;\n        }\n        try {\n            // NEW APPROACH: Create user account immediately but mark as payment_pending\n            console.log('Creating user account with payment_pending status...');\n            const { data, error } = await supabase.auth.signUp({\n                email: formData.email,\n                password: formData.password,\n                options: {\n                    data: {\n                        first_name: formData.firstName,\n                        last_name: formData.lastName,\n                        full_name: \"\".concat(formData.firstName, \" \").concat(formData.lastName),\n                        plan: selectedPlan || 'professional',\n                        payment_status: 'pending'\n                    }\n                }\n            });\n            if (error) {\n                if (error.message.includes('already registered')) {\n                    // Check if this is a pending user who can retry\n                    console.log('Email already registered, checking if user has pending payment...');\n                    try {\n                        var _signInData_user_user_metadata;\n                        // Try to sign in to check user status\n                        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({\n                            email: formData.email,\n                            password: formData.password\n                        });\n                        if (signInData.user && ((_signInData_user_user_metadata = signInData.user.user_metadata) === null || _signInData_user_user_metadata === void 0 ? void 0 : _signInData_user_user_metadata.payment_status) === 'pending') {\n                            console.log('Found pending user, allowing retry of checkout...');\n                            // Debug logging\n                            await fetch('/api/debug/checkout', {\n                                method: 'POST',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    action: 'PENDING_USER_RETRY',\n                                    userId: signInData.user.id,\n                                    email: formData.email,\n                                    selectedPlan,\n                                    message: 'Allowing pending user to retry checkout'\n                                })\n                            }).catch(()=>{});\n                            // Redirect to checkout with existing user\n                            window.location.href = \"/checkout?plan=\".concat(selectedPlan, \"&user_id=\").concat(signInData.user.id);\n                            return;\n                        } else {\n                            setError('This email is already registered with an active account. Please sign in instead.');\n                            return;\n                        }\n                    } catch (retryError) {\n                        console.error('Error checking pending user:', retryError);\n                        setError('This email is already registered. Please sign in instead.');\n                        return;\n                    }\n                } else {\n                    setError(error.message);\n                }\n                return;\n            }\n            if (!data.user) {\n                setError('Failed to create account. Please try again.');\n                return;\n            }\n            // Debug logging to terminal\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'USER_CREATED_PAYMENT_PENDING',\n                    userId: data.user.id,\n                    email: formData.email,\n                    selectedPlan,\n                    redirectUrl: \"/checkout?plan=\".concat(selectedPlan, \"&user_id=\").concat(data.user.id),\n                    paymentStatus: 'pending'\n                })\n            }).catch(()=>{});\n            console.log('User created successfully, redirecting to checkout...');\n            console.log('User ID:', data.user.id);\n            console.log('Plan selected:', selectedPlan);\n            // Redirect to checkout with authenticated user\n            // If payment fails, user will be deleted via webhook or cleanup job\n            window.location.href = \"/checkout?plan=\".concat(selectedPlan, \"&user_id=\").concat(data.user.id);\n        } catch (err) {\n            console.error('Sign up error:', err);\n            setError(err.message || 'Failed to process signup. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleGoogleSignUp = async ()=>{\n        setIsLoading(true);\n        try {\n            // Store plan selection for after OAuth\n            if (selectedPlan) {\n                localStorage.setItem('pending_plan', selectedPlan);\n            }\n            const redirectUrl = selectedPlan ? \"\".concat(window.location.origin, \"/auth/callback?redirectTo=\").concat(encodeURIComponent(\"/checkout?plan=\".concat(selectedPlan))) : \"\".concat(window.location.origin, \"/auth/callback?redirectTo=\").concat(encodeURIComponent('/dashboard'));\n            const { data, error } = await supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: redirectUrl\n                }\n            });\n            if (error) {\n                throw error;\n            }\n        // OAuth redirect will handle the rest\n        } catch (err) {\n            console.error('Google sign up error:', err);\n            setError(err.message || 'Failed to sign up with Google. Please try again.');\n            setIsLoading(false);\n        }\n    };\n    const passwordRequirements = [\n        {\n            text: 'At least 8 characters',\n            met: formData.password.length >= 8\n        },\n        {\n            text: 'Contains uppercase letter',\n            met: /[A-Z]/.test(formData.password)\n        },\n        {\n            text: 'Contains lowercase letter',\n            met: /[a-z]/.test(formData.password)\n        },\n        {\n            text: 'Contains number',\n            met: /\\d/.test(formData.password)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                gridSize: 50,\n                opacity: 0.064,\n                color: \"#000000\",\n                variant: \"subtle\",\n                animated: true,\n                className: \"fixed inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: \"radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)\",\n                        backgroundSize: '100px 100px',\n                        mask: \"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)\",\n                        WebkitMask: \"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 w-full max-w-6xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"hidden lg:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 opacity-20\",\n                                        style: {\n                                            backgroundImage: \"\\n                    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                  \",\n                                            backgroundSize: '30px 30px'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            src: \"/roukey_logo.png\",\n                                                            alt: \"RouKey\",\n                                                            width: 48,\n                                                            height: 48,\n                                                            className: \"w-12 h-12 object-contain\",\n                                                            priority: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-4xl font-bold mb-4\",\n                                                        children: \"Join RouKey Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl text-white/90 mb-8\",\n                                                        children: [\n                                                            \"Get started with \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-bold\",\n                                                                children: \"UNLIMITED\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 38\n                                                            }, this),\n                                                            \" access to 300+ AI models\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/90\",\n                                                                children: \"No Request Limits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/90\",\n                                                                children: \"300+ AI Models\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/90\",\n                                                                children: \"Enterprise Security\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            className: \"w-full max-w-md mx-auto lg:mx-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"inline-flex items-center space-x-3 mb-8 justify-center lg:justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        src: \"/roukey_logo.png\",\n                                                        alt: \"RouKey\",\n                                                        width: 40,\n                                                        height: 40,\n                                                        className: \"w-full h-full object-contain\",\n                                                        priority: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl font-bold text-black\",\n                                                    children: \"RouKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold text-black mb-3\",\n                                            children: \"Sign Up\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-lg\",\n                                            children: \"Create your AI gateway account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: async ()=>{\n                                                        await supabase.auth.signOut();\n                                                        localStorage.clear();\n                                                        window.location.reload();\n                                                    },\n                                                    className: \"text-xs text-gray-400 hover:text-gray-600\",\n                                                    children: \"\\uD83D\\uDD27 Clear Session\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setError('');\n                                                        setFormData({\n                                                            firstName: '',\n                                                            lastName: '',\n                                                            email: '',\n                                                            password: '',\n                                                            confirmPassword: ''\n                                                        });\n                                                    },\n                                                    className: \"text-xs text-gray-400 hover:text-gray-600\",\n                                                    children: \"\\uD83D\\uDDD1️ Clear Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedPlan && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-4 bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-[#ff6b35] font-semibold text-lg\",\n                                                            children: [\n                                                                selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1),\n                                                                \" Plan Selected\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-center text-gray-600 text-sm mt-1\",\n                                                    children: \"You'll be redirected to checkout after creating your account\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mt-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/pricing\",\n                                                        className: \"text-[#ff6b35] hover:text-[#e55a2b] text-sm font-medium transition-colors\",\n                                                        children: \"Change plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-5\",\n                                            style: {\n                                                backgroundImage: \"\\n                    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),\\n                    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)\\n                  \",\n                                                backgroundSize: '20px 20px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10\",\n                                            children: [\n                                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-600 text-sm\",\n                                                            children: error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        error.includes('already registered') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/auth/signin\",\n                                                                className: \"text-[#ff6b35] hover:text-[#e55a2b] font-semibold text-sm transition-colors\",\n                                                                children: \"→ Go to Sign In page\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleSubmit,\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"firstName\",\n                                                                            className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                            children: \"\\uD83D\\uDC64 First Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"firstName\",\n                                                                            name: \"firstName\",\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            value: formData.firstName,\n                                                                            onChange: handleChange,\n                                                                            className: \"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                            placeholder: \"John\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 439,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"lastName\",\n                                                                            className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                            children: \"\\uD83D\\uDC64 Last Name\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 451,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"lastName\",\n                                                                            name: \"lastName\",\n                                                                            type: \"text\",\n                                                                            required: true,\n                                                                            value: formData.lastName,\n                                                                            onChange: handleChange,\n                                                                            className: \"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                            placeholder: \"Doe\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"email\",\n                                                                    className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                    children: \"\\uD83D\\uDCE7 Email Address\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"email\",\n                                                                    name: \"email\",\n                                                                    type: \"email\",\n                                                                    autoComplete: \"email\",\n                                                                    required: true,\n                                                                    value: formData.email,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                    placeholder: \"<EMAIL>\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"password\",\n                                                                    className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                    children: \"\\uD83D\\uDD12 Password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"password\",\n                                                                            name: \"password\",\n                                                                            type: showPassword ? 'text' : 'password',\n                                                                            required: true,\n                                                                            value: formData.password,\n                                                                            onChange: handleChange,\n                                                                            className: \"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                            placeholder: \"Create a strong password\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                                            className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors\",\n                                                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                                lineNumber: 507,\n                                                                                columnNumber: 27\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                                lineNumber: 509,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 501,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                formData.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 p-4 bg-gray-50 rounded-xl space-y-2\",\n                                                                    children: passwordRequirements.map((req, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center text-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                    className: \"h-4 w-4 mr-3 \".concat(req.met ? 'text-green-500' : 'text-gray-300')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                                    lineNumber: 519,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: req.met ? 'text-green-600 font-medium' : 'text-gray-500',\n                                                                                    children: req.text\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                                    lineNumber: 520,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 518,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"confirmPassword\",\n                                                                    className: \"block text-sm font-semibold text-gray-800 mb-3\",\n                                                                    children: \"\\uD83D\\uDD12 Confirm Password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"confirmPassword\",\n                                                                            name: \"confirmPassword\",\n                                                                            type: showConfirmPassword ? 'text' : 'password',\n                                                                            required: true,\n                                                                            value: formData.confirmPassword,\n                                                                            onChange: handleChange,\n                                                                            className: \"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white\",\n                                                                            placeholder: \"Confirm your password\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                                                            className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors\",\n                                                                            children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                                lineNumber: 549,\n                                                                                columnNumber: 27\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"h-5 w-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                                lineNumber: 551,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 543,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start p-4 bg-gray-50 rounded-xl\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"terms\",\n                                                                    name: \"terms\",\n                                                                    type: \"checkbox\",\n                                                                    checked: agreedToTerms,\n                                                                    onChange: (e)=>setAgreedToTerms(e.target.checked),\n                                                                    className: \"h-5 w-5 text-[#ff6b35] focus:ring-[#ff6b35] border-gray-300 rounded-lg mt-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"terms\",\n                                                                    className: \"ml-3 block text-sm text-gray-700\",\n                                                                    children: [\n                                                                        \"I agree to the\",\n                                                                        ' ',\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                            href: \"/terms\",\n                                                                            className: \"text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors\",\n                                                                            children: \"Terms of Service\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        ' ',\n                                                                        \"and\",\n                                                                        ' ',\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                            href: \"/privacy\",\n                                                                            className: \"text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors\",\n                                                                            children: \"Privacy Policy\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            disabled: isLoading,\n                                                            className: \"w-full bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-[#e55a2b] hover:to-[#e8851a] focus:ring-4 focus:ring-[#ff6b35]/30 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02]\",\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                        lineNumber: 586,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Creating account...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 23\n                                                            }, this) : 'Create Account'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 flex items-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full border-t border-gray-200\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative flex justify-center text-sm\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-4 bg-white text-gray-500 font-medium\",\n                                                                    children: \"Or continue with\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleGoogleSignUp,\n                                                    disabled: isLoading,\n                                                    className: \"mt-6 w-full bg-white border-2 border-gray-200 text-gray-700 py-4 px-6 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-300 focus:ring-4 focus:ring-gray-200 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center shadow-sm hover:shadow-md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6 mr-3\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"#4285F4\",\n                                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"#34A853\",\n                                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 615,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"#FBBC05\",\n                                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 616,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fill: \"#EA4335\",\n                                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Continue with Google\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-lg\",\n                                        children: [\n                                            \"Already have an account?\",\n                                            ' ',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/auth/signin\",\n                                                className: \"text-[#ff6b35] hover:text-[#e55a2b] font-bold transition-colors\",\n                                                children: \"Sign in\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, this);\n}\n_s(SignUpPageContent, \"BCX0lmGvzjj8LLuQi/+8cID0LMk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams\n    ];\n});\n_c = SignUpPageContent;\nfunction SignUpPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                lineNumber: 644,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n            lineNumber: 643,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SignUpPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n            lineNumber: 650,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n        lineNumber: 642,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SignUpPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"SignUpPageContent\");\n$RefreshReg$(_c1, \"SignUpPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/signup/page.tsx\n"));

/***/ })

});