"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkout/page",{

/***/ "(app-pages-browser)/./src/app/checkout/page.tsx":
/*!***********************************!*\
  !*** ./src/app/checkout/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckoutPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CheckoutPageContent() {\n    _s();\n    // Immediate debug log\n    console.log('🔥 CheckoutPageContent function called');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__.createClientComponentClient)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const selectedPlan = searchParams.get('plan') || 'professional';\n    const userId = searchParams.get('user_id');\n    const email = searchParams.get('email');\n    const isSignup = searchParams.get('signup') === 'true';\n    // Debug the URL params immediately\n    console.log('🔍 URL params parsed:', {\n        selectedPlan,\n        userId,\n        email,\n        isSignup\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckoutPageContent.useEffect\": ()=>{\n            console.log('🚀 CheckoutPageContent component mounting...');\n            fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'COMPONENT_MOUNT',\n                    message: 'CheckoutPageContent component is mounting'\n                })\n            }).catch({\n                \"CheckoutPageContent.useEffect\": ()=>{}\n            }[\"CheckoutPageContent.useEffect\"]);\n            setMounted(true);\n        }\n    }[\"CheckoutPageContent.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckoutPageContent.useEffect\": ()=>{\n            if (!mounted) return;\n            console.log('=== CHECKOUT PAGE MOUNTED ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            console.log('Current URL:', window.location.href);\n            // Check localStorage for pending signup\n            const pendingSignup = localStorage.getItem('pending_signup');\n            console.log('localStorage pending_signup:', pendingSignup ? 'FOUND' : 'NOT FOUND');\n            if (pendingSignup) {\n                console.log('Pending signup data:', JSON.parse(pendingSignup));\n            }\n            // Add debug function to window for manual testing\n            window.debugCheckout = ({\n                \"CheckoutPageContent.useEffect\": ()=>{\n                    console.log('=== DEBUG CHECKOUT ===');\n                    console.log('localStorage pending_signup:', localStorage.getItem('pending_signup'));\n                    console.log('URL params:', {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    });\n                }\n            })[\"CheckoutPageContent.useEffect\"];\n            initializeCheckout();\n        }\n    }[\"CheckoutPageContent.useEffect\"], [\n        mounted\n    ]);\n    const initializeCheckout = async ()=>{\n        try {\n            // Send debug info to server for terminal logging\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CHECKOUT_INITIALIZATION',\n                    urlParams: {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    },\n                    currentUrl: window.location.href,\n                    localStorage: {\n                        pending_signup: localStorage.getItem('pending_signup') ? 'FOUND' : 'NOT_FOUND',\n                        pending_signup_data: localStorage.getItem('pending_signup')\n                    }\n                })\n            }).catch(()=>{}); // Don't fail if debug endpoint fails\n            console.log('=== CHECKOUT INITIALIZATION ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            // ALWAYS prioritize signup flow if URL indicates signup\n            if (isSignup) {\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'SIGNUP_FLOW_DETECTED',\n                        urlParams: {\n                            selectedPlan,\n                            userId,\n                            email,\n                            isSignup\n                        }\n                    })\n                }).catch(()=>{});\n                console.log('=== SIGNUP FLOW DETECTED ===');\n                // Check for pending signup data\n                const pendingSignup = localStorage.getItem('pending_signup');\n                console.log('Pending signup data:', pendingSignup ? 'FOUND' : 'NOT FOUND');\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'PENDING_SIGNUP_CHECK',\n                        found: !!pendingSignup,\n                        data: pendingSignup\n                    })\n                }).catch(()=>{});\n                if (pendingSignup) {\n                    const userData = JSON.parse(pendingSignup);\n                    console.log('Processing signup with data:', userData);\n                    await createCheckoutSessionForSignup(userData);\n                    return;\n                } else {\n                    console.log('ERROR: Signup URL but no localStorage data');\n                    await fetch('/api/debug/checkout', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            action: 'ERROR_NO_LOCALSTORAGE',\n                            message: 'Signup URL detected but no localStorage data found'\n                        })\n                    }).catch(()=>{});\n                    setError('Signup session expired. Please try signing up again.');\n                    setTimeout(()=>router.push(\"/auth/signup?plan=\".concat(selectedPlan)), 3000);\n                    return;\n                }\n            }\n            // EXISTING USER FLOW - Only if not a signup\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'EXISTING_USER_FLOW',\n                    message: 'No signup parameter detected, checking for existing user session'\n                })\n            }).catch(()=>{});\n            console.log('=== EXISTING USER FLOW ===');\n            const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n            console.log('Session check:', {\n                hasSession: !!session,\n                error: sessionError\n            });\n            if (sessionError || !session) {\n                console.log('No valid session - redirecting to signin');\n                setError('Authentication required. Please sign in first.');\n                setTimeout(()=>router.push(\"/auth/signin?plan=\".concat(selectedPlan)), 3000);\n                return;\n            }\n            setUser(session.user);\n            console.log('Processing existing user checkout:', session.user.id);\n            await createCheckoutSession(session.user.id);\n        } catch (error) {\n            console.error('Checkout initialization error:', error);\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'ERROR',\n                    error: error.message\n                })\n            }).catch(()=>{});\n            setError('Failed to initialize checkout. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const createCheckoutSessionForSignup = async (userData)=>{\n        try {\n            const requestData = {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userEmail: userData.email,\n                signup: true,\n                pendingUserData: userData\n            };\n            console.log('Creating checkout session for signup:', requestData);\n            console.log('Environment check:', {\n                hasStarterPrice: !!\"price_1RaA5xC97XFBBUvdt12n1i0T\",\n                hasProfessionalPrice: !!\"price_1RaABVC97XFBBUvdkZZc1oQB\",\n                hasEnterprisePrice: !!\"price_1RaADDC97XFBBUvd7j6OPJj7\"\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            const data = await response.json();\n            console.log('Checkout session response:', {\n                ok: response.ok,\n                status: response.status,\n                data\n            });\n            if (!response.ok) {\n                console.error('Checkout session creation failed:', data);\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Signup checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const createCheckoutSession = async (userId)=>{\n        try {\n            // Get user email from session or localStorage\n            let userEmail = user === null || user === void 0 ? void 0 : user.email;\n            if (!userEmail) {\n                userEmail = localStorage.getItem('pending_email');\n            }\n            if (!userEmail) {\n                throw new Error('User email not found');\n            }\n            console.log('Creating checkout session with:', {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userId: userId,\n                userEmail: userEmail\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    priceId: getPriceId(selectedPlan),\n                    tier: selectedPlan,\n                    userId: userId,\n                    userEmail: userEmail\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const getPriceId = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return \"price_1RaA5xC97XFBBUvdt12n1i0T\" || 0;\n            case 'professional':\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n            case 'enterprise':\n                return \"price_1RaADDC97XFBBUvd7j6OPJj7\" || 0;\n            default:\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n        }\n    };\n    const getPlanPrice = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return '$29';\n            case 'professional':\n                return '$99';\n            case 'enterprise':\n                return '$299';\n            default:\n                return '$99';\n        }\n    };\n    // Show loading until component is mounted (prevents hydration issues)\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading checkout...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 304,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 303,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Checkout Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                        href: \"/auth/signup?plan=\".concat(selectedPlan),\n                        className: \"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#e55a2b] transition-colors\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 315,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 314,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/roukey_logo.png\",\n                            alt: \"RouKey\",\n                            width: 40,\n                            height: 40,\n                            className: \"w-10 h-10 object-contain\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Setting up your subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#ff6b35] font-semibold\",\n                                            children: [\n                                                selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1),\n                                                \" Plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        getPlanPrice(selectedPlan),\n                                        \"/month\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"You'll be redirected to Stripe to complete your payment securely.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"After payment, you'll verify your email and gain access to your dashboard.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex items-center justify-center space-x-2 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Secured by Stripe\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 336,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 335,\n        columnNumber: 5\n    }, this);\n}\n_s(CheckoutPageContent, \"28X6dCLSY33yGsWYE1GDYj1wduQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = CheckoutPageContent;\n// Create a dynamic component to avoid hydration issues\nconst DynamicCheckoutContent = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(()=>Promise.resolve(CheckoutPageContent), {\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading checkout...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 393,\n            columnNumber: 5\n        }, undefined)\n});\n_c1 = DynamicCheckoutContent;\nfunction CheckoutPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DynamicCheckoutContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 403,\n        columnNumber: 10\n    }, this);\n}\n_c2 = CheckoutPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CheckoutPageContent\");\n$RefreshReg$(_c1, \"DynamicCheckoutContent\");\n$RefreshReg$(_c2, \"CheckoutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/checkout/page.tsx\n"));

/***/ })

});