"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkout/page",{

/***/ "(app-pages-browser)/./src/app/checkout/page.tsx":
/*!***********************************!*\
  !*** ./src/app/checkout/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckoutPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CheckoutPageContent() {\n    console.log('🔥 CheckoutPageContent function called');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActualCheckoutContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n_c = CheckoutPageContent;\nfunction ActualCheckoutContent() {\n    _s();\n    // Immediate debug log\n    console.log('🔥 ActualCheckoutContent function called');\n    // React hooks must be at the top level\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__.createClientComponentClient)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    console.log('🔥 ActualCheckoutContent - all hooks initialized');\n    const selectedPlan = searchParams.get('plan') || 'professional';\n    const userId = searchParams.get('user_id');\n    const email = searchParams.get('email');\n    const isSignup = searchParams.get('signup') === 'true';\n    // Debug the URL params immediately\n    console.log('🔍 ActualCheckoutContent URL params parsed:', {\n        selectedPlan,\n        userId,\n        email,\n        isSignup\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 ActualCheckoutContent first useEffect - component mounting...');\n            fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'COMPONENT_MOUNT',\n                    message: 'ActualCheckoutContent component is mounting'\n                })\n            }).catch({\n                \"ActualCheckoutContent.useEffect\": ()=>{}\n            }[\"ActualCheckoutContent.useEffect\"]);\n            console.log('🚀 ActualCheckoutContent - setting mounted to true');\n            setMounted(true);\n        }\n    }[\"ActualCheckoutContent.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 ActualCheckoutContent second useEffect - mounted:', mounted);\n            if (!mounted) {\n                console.log('🚀 ActualCheckoutContent - not mounted yet, returning');\n                return;\n            }\n            console.log('=== CHECKOUT PAGE MOUNTED ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            console.log('Current URL:', window.location.href);\n            // Check localStorage for pending signup\n            const pendingSignup = localStorage.getItem('pending_signup');\n            console.log('localStorage pending_signup:', pendingSignup ? 'FOUND' : 'NOT FOUND');\n            if (pendingSignup) {\n                console.log('Pending signup data:', JSON.parse(pendingSignup));\n            }\n            // Add debug function to window for manual testing\n            window.debugCheckout = ({\n                \"ActualCheckoutContent.useEffect\": ()=>{\n                    console.log('=== DEBUG CHECKOUT ===');\n                    console.log('localStorage pending_signup:', localStorage.getItem('pending_signup'));\n                    console.log('URL params:', {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    });\n                }\n            })[\"ActualCheckoutContent.useEffect\"];\n            initializeCheckout();\n        }\n    }[\"ActualCheckoutContent.useEffect\"], [\n        mounted\n    ]);\n    const createCheckoutSession = async (userId, sessionUser)=>{\n        console.log('🚀 createCheckoutSession function called with userId:', userId);\n        console.log('🚀 sessionUser parameter:', sessionUser);\n        console.log('🚀 state user:', user);\n        try {\n            var _currentUser_user_metadata, _currentUser_user_metadata1;\n            // Use passed sessionUser or fallback to state user\n            const currentUser = sessionUser || user;\n            console.log('🚀 currentUser selected:', currentUser);\n            // Get email from multiple sources\n            const userEmail = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_user_metadata = currentUser.user_metadata) === null || _currentUser_user_metadata === void 0 ? void 0 : _currentUser_user_metadata.email) || email;\n            console.log('Email extraction debug:', {\n                currentUserEmail: currentUser === null || currentUser === void 0 ? void 0 : currentUser.email,\n                currentUserMetadataEmail: currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_user_metadata1 = currentUser.user_metadata) === null || _currentUser_user_metadata1 === void 0 ? void 0 : _currentUser_user_metadata1.email,\n                urlEmail: email,\n                finalEmail: userEmail,\n                currentUserObject: currentUser\n            });\n            if (!userEmail) {\n                console.error('No email found in any source:', {\n                    currentUser,\n                    stateUser: user,\n                    urlEmail: email\n                });\n                throw new Error('User email not found');\n            }\n            console.log('Creating checkout session with:', {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userId: userId,\n                userEmail: userEmail\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    priceId: getPriceId(selectedPlan),\n                    tier: selectedPlan,\n                    userId: userId,\n                    userEmail: userEmail,\n                    signup: false\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const initializeCheckout = async ()=>{\n        try {\n            var _session_user, _session_user1;\n            // Send debug info to server for terminal logging\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CHECKOUT_INITIALIZATION',\n                    urlParams: {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    },\n                    currentUrl: window.location.href\n                })\n            }).catch(()=>{}); // Don't fail if debug endpoint fails\n            console.log('=== CHECKOUT INITIALIZATION ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            // Get current user session\n            const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n            console.log('Session check:', {\n                hasSession: !!session,\n                error: sessionError\n            });\n            // Debug session details\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'SESSION_CHECK',\n                    hasSession: !!session,\n                    sessionError: sessionError === null || sessionError === void 0 ? void 0 : sessionError.message,\n                    userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,\n                    userEmail: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email\n                })\n            }).catch(()=>{});\n            if (sessionError || !session) {\n                console.log('No valid session - redirecting to signup');\n                console.log('Session error:', sessionError);\n                console.log('Session data:', session);\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'SESSION_FAILED',\n                        error: (sessionError === null || sessionError === void 0 ? void 0 : sessionError.message) || 'No session found',\n                        redirecting: true\n                    })\n                }).catch(()=>{});\n                setError('Authentication required. Please sign up first.');\n                setTimeout(()=>router.push(\"/auth/signup?plan=\".concat(selectedPlan)), 3000);\n                return;\n            }\n            setUser(session.user);\n            console.log('Set user state:', session.user);\n            // Check if user has payment_pending status (new signup)\n            const userMetadata = session.user.user_metadata;\n            const paymentStatus = userMetadata === null || userMetadata === void 0 ? void 0 : userMetadata.payment_status;\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'USER_SESSION_FOUND',\n                    userId: session.user.id,\n                    email: session.user.email,\n                    paymentStatus,\n                    userMetadata\n                })\n            }).catch(()=>{});\n            console.log('Processing checkout for user:', session.user.id);\n            console.log('Payment status:', paymentStatus);\n            // Debug before calling createCheckoutSession\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CALLING_CREATE_CHECKOUT_SESSION',\n                    userId: session.user.id,\n                    selectedPlan,\n                    aboutToCall: true\n                })\n            }).catch(()=>{});\n            console.log('About to call createCheckoutSession...');\n            try {\n                // Create checkout session for authenticated user\n                // Pass the session user directly instead of relying on state\n                console.log('Calling createCheckoutSession with session user:', session.user);\n                // Add extra safety check\n                if (typeof createCheckoutSession !== 'function') {\n                    throw new Error('createCheckoutSession is not a function');\n                }\n                await createCheckoutSession(session.user.id, session.user);\n                console.log('createCheckoutSession call completed successfully');\n            } catch (checkoutError) {\n                console.error('Error in createCheckoutSession:', checkoutError);\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'CREATE_CHECKOUT_SESSION_ERROR',\n                        error: checkoutError.message,\n                        stack: checkoutError.stack\n                    })\n                }).catch(()=>{});\n                throw checkoutError; // Re-throw to be caught by outer catch\n            }\n        } catch (error) {\n            console.error('Checkout initialization error:', error);\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'ERROR',\n                    error: error.message\n                })\n            }).catch(()=>{});\n            setError('Failed to initialize checkout. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const createCheckoutSessionForSignup = async (userData)=>{\n        try {\n            const requestData = {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userEmail: userData.email,\n                signup: true,\n                pendingUserData: userData\n            };\n            console.log('Creating checkout session for signup:', requestData);\n            console.log('Environment check:', {\n                hasStarterPrice: !!\"price_1RaA5xC97XFBBUvdt12n1i0T\",\n                hasProfessionalPrice: !!\"price_1RaABVC97XFBBUvdkZZc1oQB\",\n                hasEnterprisePrice: !!\"price_1RaADDC97XFBBUvd7j6OPJj7\"\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            const data = await response.json();\n            console.log('Checkout session response:', {\n                ok: response.ok,\n                status: response.status,\n                data\n            });\n            if (!response.ok) {\n                console.error('Checkout session creation failed:', data);\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Signup checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const getPriceId = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return \"price_1RaA5xC97XFBBUvdt12n1i0T\" || 0;\n            case 'professional':\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n            case 'enterprise':\n                return \"price_1RaADDC97XFBBUvd7j6OPJj7\" || 0;\n            default:\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n        }\n    };\n    const getPlanPrice = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return '$29';\n            case 'professional':\n                return '$99';\n            case 'enterprise':\n                return '$299';\n            default:\n                return '$99';\n        }\n    };\n    // Show loading until component is mounted (prevents hydration issues)\n    if (!mounted) {\n        console.log('⏳ Component not mounted yet, showing loading...');\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading checkout...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 363,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 362,\n            columnNumber: 7\n        }, this);\n    }\n    console.log('✅ Component mounted, proceeding with render...');\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Checkout Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                        href: \"/auth/signup?plan=\".concat(selectedPlan),\n                        className: \"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#e55a2b] transition-colors\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 376,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 375,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/roukey_logo.png\",\n                            alt: \"RouKey\",\n                            width: 40,\n                            height: 40,\n                            className: \"w-10 h-10 object-contain\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 399,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Setting up your subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#ff6b35] font-semibold\",\n                                            children: [\n                                                selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1),\n                                                \" Plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        getPlanPrice(selectedPlan),\n                                        \"/month\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"You'll be redirected to Stripe to complete your payment securely.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"After payment, you'll verify your email and gain access to your dashboard.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex items-center justify-center space-x-2 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Secured by Stripe\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 397,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 396,\n        columnNumber: 5\n    }, this);\n}\n_s(ActualCheckoutContent, \"28X6dCLSY33yGsWYE1GDYj1wduQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c1 = ActualCheckoutContent;\nfunction CheckoutPage() {\n    console.log('🚀 CheckoutPage main function called');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckoutPageContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 452,\n        columnNumber: 10\n    }, this);\n}\n_c2 = CheckoutPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CheckoutPageContent\");\n$RefreshReg$(_c1, \"ActualCheckoutContent\");\n$RefreshReg$(_c2, \"CheckoutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/checkout/page.tsx\n"));

/***/ })

});