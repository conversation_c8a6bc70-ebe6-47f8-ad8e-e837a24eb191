"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkout/page",{

/***/ "(app-pages-browser)/./src/app/checkout/page.tsx":
/*!***********************************!*\
  !*** ./src/app/checkout/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckoutPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction CheckoutPageContent() {\n    _s();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Only run on client side\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CheckoutPageContent.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"CheckoutPageContent.useEffect\"], []);\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading checkout...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActualCheckoutContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 28,\n        columnNumber: 10\n    }, this);\n}\n_s(CheckoutPageContent, \"k460N28PNzD7zo1YW47Q9UigQis=\");\n_c = CheckoutPageContent;\nfunction ActualCheckoutContent() {\n    _s1();\n    // Immediate debug log\n    console.log('🔥 ActualCheckoutContent function called');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__.createClientComponentClient)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const selectedPlan = searchParams.get('plan') || 'professional';\n    const userId = searchParams.get('user_id');\n    const email = searchParams.get('email');\n    const isSignup = searchParams.get('signup') === 'true';\n    // Debug the URL params immediately\n    console.log('🔍 URL params parsed:', {\n        selectedPlan,\n        userId,\n        email,\n        isSignup\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 CheckoutPageContent component mounting...');\n            fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'COMPONENT_MOUNT',\n                    message: 'CheckoutPageContent component is mounting'\n                })\n            }).catch({\n                \"ActualCheckoutContent.useEffect\": ()=>{}\n            }[\"ActualCheckoutContent.useEffect\"]);\n            setMounted(true);\n        }\n    }[\"ActualCheckoutContent.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            if (!mounted) return;\n            console.log('=== CHECKOUT PAGE MOUNTED ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            console.log('Current URL:', window.location.href);\n            // Check localStorage for pending signup\n            const pendingSignup = localStorage.getItem('pending_signup');\n            console.log('localStorage pending_signup:', pendingSignup ? 'FOUND' : 'NOT FOUND');\n            if (pendingSignup) {\n                console.log('Pending signup data:', JSON.parse(pendingSignup));\n            }\n            // Add debug function to window for manual testing\n            window.debugCheckout = ({\n                \"ActualCheckoutContent.useEffect\": ()=>{\n                    console.log('=== DEBUG CHECKOUT ===');\n                    console.log('localStorage pending_signup:', localStorage.getItem('pending_signup'));\n                    console.log('URL params:', {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    });\n                }\n            })[\"ActualCheckoutContent.useEffect\"];\n            initializeCheckout();\n        }\n    }[\"ActualCheckoutContent.useEffect\"], [\n        mounted\n    ]);\n    const initializeCheckout = async ()=>{\n        try {\n            // Send debug info to server for terminal logging\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CHECKOUT_INITIALIZATION',\n                    urlParams: {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    },\n                    currentUrl: window.location.href,\n                    localStorage: {\n                        pending_signup: localStorage.getItem('pending_signup') ? 'FOUND' : 'NOT_FOUND',\n                        pending_signup_data: localStorage.getItem('pending_signup')\n                    }\n                })\n            }).catch(()=>{}); // Don't fail if debug endpoint fails\n            console.log('=== CHECKOUT INITIALIZATION ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            // ALWAYS prioritize signup flow if URL indicates signup\n            console.log('🔍 Checking isSignup:', isSignup, typeof isSignup);\n            if (isSignup) {\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'SIGNUP_FLOW_DETECTED',\n                        urlParams: {\n                            selectedPlan,\n                            userId,\n                            email,\n                            isSignup\n                        },\n                        isSignupType: typeof isSignup,\n                        isSignupValue: isSignup\n                    })\n                }).catch(()=>{});\n                console.log('=== SIGNUP FLOW DETECTED ===');\n                // Check for pending signup data\n                const pendingSignup = localStorage.getItem('pending_signup');\n                console.log('Pending signup data:', pendingSignup ? 'FOUND' : 'NOT FOUND');\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'PENDING_SIGNUP_CHECK',\n                        found: !!pendingSignup,\n                        data: pendingSignup\n                    })\n                }).catch(()=>{});\n                if (pendingSignup) {\n                    const userData = JSON.parse(pendingSignup);\n                    console.log('Processing signup with data:', userData);\n                    await createCheckoutSessionForSignup(userData);\n                    return;\n                } else {\n                    console.log('ERROR: Signup URL but no localStorage data');\n                    await fetch('/api/debug/checkout', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            action: 'ERROR_NO_LOCALSTORAGE',\n                            message: 'Signup URL detected but no localStorage data found'\n                        })\n                    }).catch(()=>{});\n                    setError('Signup session expired. Please try signing up again.');\n                    setTimeout(()=>router.push(\"/auth/signup?plan=\".concat(selectedPlan)), 3000);\n                    return;\n                }\n            }\n            // EXISTING USER FLOW - Only if not a signup\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'EXISTING_USER_FLOW',\n                    message: 'No signup parameter detected, checking for existing user session'\n                })\n            }).catch(()=>{});\n            console.log('=== EXISTING USER FLOW ===');\n            const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n            console.log('Session check:', {\n                hasSession: !!session,\n                error: sessionError\n            });\n            if (sessionError || !session) {\n                console.log('No valid session - redirecting to signin');\n                setError('Authentication required. Please sign in first.');\n                setTimeout(()=>router.push(\"/auth/signin?plan=\".concat(selectedPlan)), 3000);\n                return;\n            }\n            setUser(session.user);\n            console.log('Processing existing user checkout:', session.user.id);\n            await createCheckoutSession(session.user.id);\n        } catch (error) {\n            console.error('Checkout initialization error:', error);\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'ERROR',\n                    error: error.message\n                })\n            }).catch(()=>{});\n            setError('Failed to initialize checkout. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const createCheckoutSessionForSignup = async (userData)=>{\n        try {\n            const requestData = {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userEmail: userData.email,\n                signup: true,\n                pendingUserData: userData\n            };\n            console.log('Creating checkout session for signup:', requestData);\n            console.log('Environment check:', {\n                hasStarterPrice: !!\"price_1RaA5xC97XFBBUvdt12n1i0T\",\n                hasProfessionalPrice: !!\"price_1RaABVC97XFBBUvdkZZc1oQB\",\n                hasEnterprisePrice: !!\"price_1RaADDC97XFBBUvd7j6OPJj7\"\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            const data = await response.json();\n            console.log('Checkout session response:', {\n                ok: response.ok,\n                status: response.status,\n                data\n            });\n            if (!response.ok) {\n                console.error('Checkout session creation failed:', data);\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Signup checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const createCheckoutSession = async (userId)=>{\n        try {\n            // Get user email from session or localStorage\n            let userEmail = user === null || user === void 0 ? void 0 : user.email;\n            if (!userEmail) {\n                userEmail = localStorage.getItem('pending_email');\n            }\n            if (!userEmail) {\n                throw new Error('User email not found');\n            }\n            console.log('Creating checkout session with:', {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userId: userId,\n                userEmail: userEmail\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    priceId: getPriceId(selectedPlan),\n                    tier: selectedPlan,\n                    userId: userId,\n                    userEmail: userEmail\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const getPriceId = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return \"price_1RaA5xC97XFBBUvdt12n1i0T\" || 0;\n            case 'professional':\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n            case 'enterprise':\n                return \"price_1RaADDC97XFBBUvd7j6OPJj7\" || 0;\n            default:\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n        }\n    };\n    const getPlanPrice = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return '$29';\n            case 'professional':\n                return '$99';\n            case 'enterprise':\n                return '$299';\n            default:\n                return '$99';\n        }\n    };\n    // Show loading until component is mounted (prevents hydration issues)\n    if (!mounted) {\n        console.log('⏳ Component not mounted yet, showing loading...');\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading checkout...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 329,\n            columnNumber: 7\n        }, this);\n    }\n    console.log('✅ Component mounted, proceeding with render...');\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Checkout Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                        href: \"/auth/signup?plan=\".concat(selectedPlan),\n                        className: \"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#e55a2b] transition-colors\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 343,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 342,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/roukey_logo.png\",\n                            alt: \"RouKey\",\n                            width: 40,\n                            height: 40,\n                            className: \"w-10 h-10 object-contain\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Setting up your subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#ff6b35] font-semibold\",\n                                            children: [\n                                                selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1),\n                                                \" Plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        getPlanPrice(selectedPlan),\n                                        \"/month\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"You'll be redirected to Stripe to complete your payment securely.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"After payment, you'll verify your email and gain access to your dashboard.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex items-center justify-center space-x-2 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Secured by Stripe\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 364,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 363,\n        columnNumber: 5\n    }, this);\n}\n_s1(ActualCheckoutContent, \"28X6dCLSY33yGsWYE1GDYj1wduQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c1 = ActualCheckoutContent;\nfunction CheckoutPage() {\n    console.log('🚀 CheckoutPage main function called');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckoutPageContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 419,\n        columnNumber: 10\n    }, this);\n}\n_c2 = CheckoutPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CheckoutPageContent\");\n$RefreshReg$(_c1, \"ActualCheckoutContent\");\n$RefreshReg$(_c2, \"CheckoutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/checkout/page.tsx\n"));

/***/ })

});