-- Create a test user with active subscription for testing the payment-first flow
-- This is for development/testing purposes only

-- Note: This should only be run in development/testing environments
-- In production, users should only be created through the payment flow

-- First, let's create a test user in auth.users (this would normally be done by Stripe webhook)
-- We'll use the admin API in the application instead of doing this directly

-- For now, let's just update the existing user to have an active subscription for testing
-- This simulates what would happen after a successful payment

-- Update the existing user to have active subscription status
UPDATE public.user_profiles 
SET 
    subscription_status = 'active',
    subscription_tier = 'professional',
    updated_at = now()
WHERE id = '2cd960f9-ac04-4ea8-af8c-1b67e292f7e8';

-- Create a corresponding subscription record for this user
INSERT INTO public.subscriptions (
    user_id,
    stripe_customer_id,
    stripe_subscription_id,
    tier,
    status,
    current_period_start,
    current_period_end,
    cancel_at_period_end,
    created_at,
    updated_at
) VALUES (
    '2cd960f9-ac04-4ea8-af8c-1b67e292f7e8',
    'cus_test_customer_id',
    'sub_test_subscription_id',
    'professional',
    'active',
    now(),
    now() + interval '1 month',
    false,
    now(),
    now()
) ON CONFLICT (stripe_subscription_id) DO NOTHING;

-- Add a comment to track this is a test record
COMMENT ON TABLE public.subscriptions IS 'Subscription records synced with Stripe. Test records have stripe_subscription_id starting with "sub_test_"';
