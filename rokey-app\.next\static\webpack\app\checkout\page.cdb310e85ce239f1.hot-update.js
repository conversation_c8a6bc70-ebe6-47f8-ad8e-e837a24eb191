"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/checkout/page",{

/***/ "(app-pages-browser)/./src/app/checkout/page.tsx":
/*!***********************************!*\
  !*** ./src/app/checkout/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CheckoutPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CheckoutPageContent() {\n    console.log('🔥 CheckoutPageContent function called');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActualCheckoutContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n_c = CheckoutPageContent;\nfunction ActualCheckoutContent() {\n    _s();\n    // Immediate debug log\n    console.log('🔥 ActualCheckoutContent function called');\n    // React hooks must be at the top level\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_3__.createClientComponentClient)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    console.log('🔥 ActualCheckoutContent - all hooks initialized');\n    const selectedPlan = searchParams.get('plan') || 'professional';\n    const userId = searchParams.get('user_id');\n    const email = searchParams.get('email');\n    const isSignup = searchParams.get('signup') === 'true';\n    // Debug the URL params immediately\n    console.log('🔍 ActualCheckoutContent URL params parsed:', {\n        selectedPlan,\n        userId,\n        email,\n        isSignup\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 ActualCheckoutContent first useEffect - component mounting...');\n            fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'COMPONENT_MOUNT',\n                    message: 'ActualCheckoutContent component is mounting'\n                })\n            }).catch({\n                \"ActualCheckoutContent.useEffect\": ()=>{}\n            }[\"ActualCheckoutContent.useEffect\"]);\n            console.log('🚀 ActualCheckoutContent - setting mounted to true');\n            setMounted(true);\n        }\n    }[\"ActualCheckoutContent.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ActualCheckoutContent.useEffect\": ()=>{\n            console.log('🚀 ActualCheckoutContent second useEffect - mounted:', mounted);\n            if (!mounted) {\n                console.log('🚀 ActualCheckoutContent - not mounted yet, returning');\n                return;\n            }\n            console.log('=== CHECKOUT PAGE MOUNTED ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            console.log('Current URL:', window.location.href);\n            // Check localStorage for pending signup\n            const pendingSignup = localStorage.getItem('pending_signup');\n            console.log('localStorage pending_signup:', pendingSignup ? 'FOUND' : 'NOT FOUND');\n            if (pendingSignup) {\n                console.log('Pending signup data:', JSON.parse(pendingSignup));\n            }\n            // Add debug function to window for manual testing\n            window.debugCheckout = ({\n                \"ActualCheckoutContent.useEffect\": ()=>{\n                    console.log('=== DEBUG CHECKOUT ===');\n                    console.log('localStorage pending_signup:', localStorage.getItem('pending_signup'));\n                    console.log('URL params:', {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    });\n                }\n            })[\"ActualCheckoutContent.useEffect\"];\n            initializeCheckout();\n        }\n    }[\"ActualCheckoutContent.useEffect\"], [\n        mounted\n    ]);\n    const initializeCheckout = async ()=>{\n        try {\n            var _session_user, _session_user1;\n            // Send debug info to server for terminal logging\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CHECKOUT_INITIALIZATION',\n                    urlParams: {\n                        selectedPlan,\n                        userId,\n                        email,\n                        isSignup\n                    },\n                    currentUrl: window.location.href\n                })\n            }).catch(()=>{}); // Don't fail if debug endpoint fails\n            console.log('=== CHECKOUT INITIALIZATION ===');\n            console.log('URL params:', {\n                selectedPlan,\n                userId,\n                email,\n                isSignup\n            });\n            // Get current user session\n            const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n            console.log('Session check:', {\n                hasSession: !!session,\n                error: sessionError\n            });\n            // Debug session details\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'SESSION_CHECK',\n                    hasSession: !!session,\n                    sessionError: sessionError === null || sessionError === void 0 ? void 0 : sessionError.message,\n                    userId: session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,\n                    userEmail: session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.email\n                })\n            }).catch(()=>{});\n            if (sessionError || !session) {\n                console.log('No valid session - redirecting to signup');\n                console.log('Session error:', sessionError);\n                console.log('Session data:', session);\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'SESSION_FAILED',\n                        error: (sessionError === null || sessionError === void 0 ? void 0 : sessionError.message) || 'No session found',\n                        redirecting: true\n                    })\n                }).catch(()=>{});\n                setError('Authentication required. Please sign up first.');\n                setTimeout(()=>router.push(\"/auth/signup?plan=\".concat(selectedPlan)), 3000);\n                return;\n            }\n            setUser(session.user);\n            console.log('Set user state:', session.user);\n            // Check if user has payment_pending status (new signup)\n            const userMetadata = session.user.user_metadata;\n            const paymentStatus = userMetadata === null || userMetadata === void 0 ? void 0 : userMetadata.payment_status;\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'USER_SESSION_FOUND',\n                    userId: session.user.id,\n                    email: session.user.email,\n                    paymentStatus,\n                    userMetadata\n                })\n            }).catch(()=>{});\n            console.log('Processing checkout for user:', session.user.id);\n            console.log('Payment status:', paymentStatus);\n            // Debug before calling createCheckoutSession\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'CALLING_CREATE_CHECKOUT_SESSION',\n                    userId: session.user.id,\n                    selectedPlan,\n                    aboutToCall: true\n                })\n            }).catch(()=>{});\n            console.log('About to call createCheckoutSession...');\n            try {\n                // Create checkout session for authenticated user\n                // Pass the session user directly instead of relying on state\n                console.log('Calling createCheckoutSession with session user:', session.user);\n                await createCheckoutSession(session.user.id, session.user);\n                console.log('createCheckoutSession call completed successfully');\n            } catch (checkoutError) {\n                console.error('Error in createCheckoutSession:', checkoutError);\n                await fetch('/api/debug/checkout', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'CREATE_CHECKOUT_SESSION_ERROR',\n                        error: checkoutError.message,\n                        stack: checkoutError.stack\n                    })\n                }).catch(()=>{});\n                throw checkoutError; // Re-throw to be caught by outer catch\n            }\n        } catch (error) {\n            console.error('Checkout initialization error:', error);\n            await fetch('/api/debug/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'ERROR',\n                    error: error.message\n                })\n            }).catch(()=>{});\n            setError('Failed to initialize checkout. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const createCheckoutSessionForSignup = async (userData)=>{\n        try {\n            const requestData = {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userEmail: userData.email,\n                signup: true,\n                pendingUserData: userData\n            };\n            console.log('Creating checkout session for signup:', requestData);\n            console.log('Environment check:', {\n                hasStarterPrice: !!\"price_1RaA5xC97XFBBUvdt12n1i0T\",\n                hasProfessionalPrice: !!\"price_1RaABVC97XFBBUvdkZZc1oQB\",\n                hasEnterprisePrice: !!\"price_1RaADDC97XFBBUvd7j6OPJj7\"\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            const data = await response.json();\n            console.log('Checkout session response:', {\n                ok: response.ok,\n                status: response.status,\n                data\n            });\n            if (!response.ok) {\n                console.error('Checkout session creation failed:', data);\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Signup checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const createCheckoutSession = async (userId, sessionUser)=>{\n        console.log('🚀 createCheckoutSession function called with userId:', userId);\n        console.log('🚀 sessionUser parameter:', sessionUser);\n        console.log('🚀 state user:', user);\n        try {\n            var _currentUser_user_metadata, _currentUser_user_metadata1;\n            // Use passed sessionUser or fallback to state user\n            const currentUser = sessionUser || user;\n            console.log('🚀 currentUser selected:', currentUser);\n            // Get email from multiple sources\n            const userEmail = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email) || (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_user_metadata = currentUser.user_metadata) === null || _currentUser_user_metadata === void 0 ? void 0 : _currentUser_user_metadata.email) || email;\n            console.log('Email extraction debug:', {\n                currentUserEmail: currentUser === null || currentUser === void 0 ? void 0 : currentUser.email,\n                currentUserMetadataEmail: currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_user_metadata1 = currentUser.user_metadata) === null || _currentUser_user_metadata1 === void 0 ? void 0 : _currentUser_user_metadata1.email,\n                urlEmail: email,\n                finalEmail: userEmail,\n                currentUserObject: currentUser\n            });\n            if (!userEmail) {\n                console.error('No email found in any source:', {\n                    currentUser,\n                    stateUser: user,\n                    urlEmail: email\n                });\n                throw new Error('User email not found');\n            }\n            console.log('Creating checkout session with:', {\n                priceId: getPriceId(selectedPlan),\n                tier: selectedPlan,\n                userId: userId,\n                userEmail: userEmail\n            });\n            const response = await fetch('/api/stripe/create-checkout-session', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    priceId: getPriceId(selectedPlan),\n                    tier: selectedPlan,\n                    userId: userId,\n                    userEmail: userEmail,\n                    signup: false\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to create checkout session');\n            }\n            // Redirect to Stripe Checkout\n            if (data.url) {\n                window.location.href = data.url;\n            } else {\n                throw new Error('No checkout URL received');\n            }\n        } catch (error) {\n            console.error('Checkout session error:', error);\n            setError(error instanceof Error ? error.message : 'Failed to start checkout');\n        }\n    };\n    const getPriceId = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return \"price_1RaA5xC97XFBBUvdt12n1i0T\" || 0;\n            case 'professional':\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n            case 'enterprise':\n                return \"price_1RaADDC97XFBBUvd7j6OPJj7\" || 0;\n            default:\n                return \"price_1RaABVC97XFBBUvdkZZc1oQB\" || 0;\n        }\n    };\n    const getPlanPrice = (plan)=>{\n        switch(plan.toLowerCase()){\n            case 'starter':\n                return '$29';\n            case 'professional':\n                return '$99';\n            case 'enterprise':\n                return '$299';\n            default:\n                return '$99';\n        }\n    };\n    // Show loading until component is mounted (prevents hydration issues)\n    if (!mounted) {\n        console.log('⏳ Component not mounted yet, showing loading...');\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading checkout...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 354,\n            columnNumber: 7\n        }, this);\n    }\n    console.log('✅ Component mounted, proceeding with render...');\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-8 h-8 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Checkout Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                        href: \"/auth/signup?plan=\".concat(selectedPlan),\n                        className: \"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#e55a2b] transition-colors\",\n                        children: \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                lineNumber: 368,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 367,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-white z-[9999] flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/roukey_logo.png\",\n                            alt: \"RouKey\",\n                            width: 40,\n                            height: 40,\n                            className: \"w-10 h-10 object-contain\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl shadow-xl border border-gray-100 p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 border-4 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Setting up your subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl p-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#ff6b35] font-semibold\",\n                                            children: [\n                                                selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1),\n                                                \" Plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-[#ff6b35] rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        getPlanPrice(selectedPlan),\n                                        \"/month\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"You'll be redirected to Stripe to complete your payment securely.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"After payment, you'll verify your email and gain access to your dashboard.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 flex items-center justify-center space-x-2 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Secured by Stripe\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n                    lineNumber: 431,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n            lineNumber: 389,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 388,\n        columnNumber: 5\n    }, this);\n}\n_s(ActualCheckoutContent, \"28X6dCLSY33yGsWYE1GDYj1wduQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c1 = ActualCheckoutContent;\nfunction CheckoutPage() {\n    console.log('🚀 CheckoutPage main function called');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckoutPageContent, {}, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\checkout\\\\page.tsx\",\n        lineNumber: 444,\n        columnNumber: 10\n    }, this);\n}\n_c2 = CheckoutPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CheckoutPageContent\");\n$RefreshReg$(_c1, \"ActualCheckoutContent\");\n$RefreshReg$(_c2, \"CheckoutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/checkout/page.tsx\n"));

/***/ })

});