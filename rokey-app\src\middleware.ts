import { createServerClient } from '@supabase/ssr';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(req: NextRequest) {
  let res = NextResponse.next({
    request: {
      headers: req.headers,
    },
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          req.cookies.set({
            name,
            value,
            ...options,
          });
          res = NextResponse.next({
            request: {
              headers: req.headers,
            },
          });
          res.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name: string, options: any) {
          req.cookies.set({
            name,
            value: '',
            ...options,
          });
          res = NextResponse.next({
            request: {
              headers: req.headers,
            },
          });
          res.cookies.set({
            name,
            value: '',
            ...options,
          });
        },
      },
    }
  );

  // Get the pathname
  const pathname = req.nextUrl.pathname;

  // Public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/pricing',
    '/auth/signin',
    '/auth/signup',
    '/auth/callback',
    '/auth/verify-email',
    '/checkout', // Allow checkout for pending signups
    '/api/stripe/webhooks', // Stripe webhooks should be public
  ];

  // API routes that don't require authentication
  const publicApiRoutes = [
    '/api/stripe/webhooks',
    '/api/system-status',
  ];

  // Check if the route is public
  const isPublicRoute = publicRoutes.some(route => pathname === route || pathname.startsWith(route));
  const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route));

  // Allow public routes and API routes
  if (isPublicRoute || isPublicApiRoute) {
    return res;
  }

  // Get the session
  const {
    data: { session },
  } = await supabase.auth.getSession();

  // If no session and trying to access protected route, redirect to signin
  if (!session) {
    const redirectUrl = new URL('/auth/signin', req.url);
    redirectUrl.searchParams.set('redirectTo', pathname);
    return NextResponse.redirect(redirectUrl);
  }

  // For authenticated users, check if they're accessing dashboard routes
  if (pathname.startsWith('/dashboard') || pathname.startsWith('/playground') || pathname.startsWith('/logs') || pathname.startsWith('/my-models') || pathname.startsWith('/api-keys') || pathname.startsWith('/configurations')) {
    // Check subscription status for protected app routes
    try {
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('subscription_status, subscription_tier')
        .eq('id', session.user.id)
        .single();

      // If no profile or inactive subscription, redirect to pricing
      if (!profile || profile.subscription_status !== 'active') {
        const redirectUrl = new URL('/pricing', req.url);
        redirectUrl.searchParams.set('checkout', 'true');
        redirectUrl.searchParams.set('message', 'subscription_required');
        return NextResponse.redirect(redirectUrl);
      }
    } catch (error) {
      console.error('Error checking subscription status in middleware:', error);
      // On error, redirect to pricing to be safe
      const redirectUrl = new URL('/pricing', req.url);
      redirectUrl.searchParams.set('checkout', 'true');
      redirectUrl.searchParams.set('message', 'subscription_check_failed');
      return NextResponse.redirect(redirectUrl);
    }
  }

  return res;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
